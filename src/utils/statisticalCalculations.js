/**
 * Advanced Statistical Calculations Utility
 * Provides functions for statistical operations beyond basic aggregations
 */

/**
 * Calculate variance of a numeric array
 * @param {number[]} values - Array of numeric values
 * @param {boolean} sample - Whether to calculate sample variance (default: false for population variance)
 * @returns {number} Variance value
 */
export function calculateVariance(values, sample = false) {
  if (!values || values.length === 0) return 0

  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  const squaredDiffs = values.map((val) => Math.pow(val - mean, 2))
  const sumSquaredDiffs = squaredDiffs.reduce((sum, val) => sum + val, 0)

  const divisor = sample ? values.length - 1 : values.length
  return divisor > 0 ? sumSquaredDiffs / divisor : 0
}

/**
 * Calculate standard deviation of a numeric array
 * @param {number[]} values - Array of numeric values
 * @param {boolean} sample - Whether to calculate sample standard deviation (default: false)
 * @returns {number} Standard deviation value
 */
export function calculateStandardDeviation(values, sample = false) {
  return Math.sqrt(calculateVariance(values, sample))
}

/**
 * Calculate quantile/percentile of a numeric array
 * @param {number[]} values - Array of numeric values
 * @param {number} percentile - Percentile to calculate (0-100)
 * @returns {number} Quantile value
 */
export function calculateQuantile(values, percentile) {
  if (!values || values.length === 0) return 0
  if (percentile < 0 || percentile > 100) return 0

  const sorted = [...values].sort((a, b) => a - b)
  const index = (percentile / 100) * (sorted.length - 1)

  if (Number.isInteger(index)) {
    return sorted[index]
  } else {
    const lower = Math.floor(index)
    const upper = Math.ceil(index)
    const weight = index - lower
    return sorted[lower] * (1 - weight) + sorted[upper] * weight
  }
}

/**
 * Calculate mode (most frequent value) of a numeric array
 * @param {number[]} values - Array of numeric values
 * @returns {number|null} Mode value or null if no mode exists
 */
export function calculateMode(values) {
  if (!values || values.length === 0) return null

  const frequency = {}
  let maxFreq = 0
  let modes = []

  // Count frequencies
  values.forEach((val) => {
    frequency[val] = (frequency[val] || 0) + 1
    if (frequency[val] > maxFreq) {
      maxFreq = frequency[val]
    }
  })

  // Find all values with maximum frequency
  Object.keys(frequency).forEach((val) => {
    if (frequency[val] === maxFreq) {
      modes.push(Number(val))
    }
  })

  // Return the smallest mode if multiple modes exist, or null if all values appear once
  return maxFreq > 1 ? Math.min(...modes) : null
}

/**
 * Calculate coefficient of variation (CV) of a numeric array
 * @param {number[]} values - Array of numeric values
 * @returns {number} Coefficient of variation as percentage
 */
export function calculateCoefficientOfVariation(values) {
  if (!values || values.length === 0) return 0

  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  if (mean === 0) return 0

  const stdDev = calculateStandardDeviation(values)
  return (stdDev / Math.abs(mean)) * 100
}

/**
 * Calculate skewness of a numeric array
 * @param {number[]} values - Array of numeric values
 * @returns {number} Skewness value
 */
export function calculateSkewness(values) {
  if (!values || values.length < 3) return 0

  const n = values.length
  const mean = values.reduce((sum, val) => sum + val, 0) / n
  const stdDev = calculateStandardDeviation(values)

  if (stdDev === 0) return 0

  const sumCubedDeviations = values.reduce((sum, val) => {
    return sum + Math.pow((val - mean) / stdDev, 3)
  }, 0)

  return (n / ((n - 1) * (n - 2))) * sumCubedDeviations
}

/**
 * Calculate kurtosis of a numeric array
 * @param {number[]} values - Array of numeric values
 * @returns {number} Kurtosis value (excess kurtosis, normal distribution = 0)
 */
export function calculateKurtosis(values) {
  if (!values || values.length < 4) return 0

  const n = values.length
  const mean = values.reduce((sum, val) => sum + val, 0) / n
  const stdDev = calculateStandardDeviation(values)

  if (stdDev === 0) return 0

  const sumFourthPowers = values.reduce((sum, val) => {
    return sum + Math.pow((val - mean) / stdDev, 4)
  }, 0)

  const kurtosis = ((n * (n + 1)) / ((n - 1) * (n - 2) * (n - 3))) * sumFourthPowers
  const correction = (3 * Math.pow(n - 1, 2)) / ((n - 2) * (n - 3))

  return kurtosis - correction // Excess kurtosis
}

/**
 * Format statistical values for display
 * @param {number} value - The statistical value to format
 * @param {string} type - The type of statistic for appropriate formatting
 * @returns {string} Formatted value
 */
export function formatStatisticalValue(value, type) {
  if (value === null || value === undefined || isNaN(value)) return '-'

  switch (type) {
    case 'variance':
    case 'standardDeviation':
    case 'quantile':
      return Number(value).toFixed(2)
    case 'coefficientOfVariation':
      return Number(value).toFixed(2) + '%'
    case 'skewness':
    case 'kurtosis':
      return Number(value).toFixed(3)
    case 'mode':
      return value === null ? '无' : Number(value).toLocaleString()
    default:
      return Number(value).toFixed(2)
  }
}

/**
 * Get statistical calculation labels in Chinese
 */
export const STATISTICAL_LABELS = {
  sum: '求和',
  avg: '平均值',
  variance: '方差',
  standardDeviation: '标准差',
  max: '最大值',
  min: '最小值',
  quantile: '分位点',
  mode: '众数',
  coefficientOfVariation: '变异系数',
  skewness: '偏度',
  kurtosis: '峰度',
  count: '计数',
}

/**
 * Get statistical calculation icons
 */
export const STATISTICAL_ICONS = {
  sum: '∑',
  avg: '⌀',
  variance: 'σ²',
  standardDeviation: 'σ',
  max: '⬆',
  min: '⬇',
  quantile: 'Q',
  mode: 'M',
  coefficientOfVariation: 'CV',
  skewness: 'S',
  kurtosis: 'K',
  count: '#',
}
