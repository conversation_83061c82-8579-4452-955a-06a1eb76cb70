import axios from 'axios'
import { handleError } from './errorHandler'

/**
 * Fetch task list from the API
 * @param {string} taskName - Optional task name for fuzzy search filtering
 * @param {number} pageNum - Page number for pagination (default: 0)
 * @returns {Promise<Object>} API response with task data and pagination info
 */
export const fetchTaskList = async (taskName = '', pageNum = 0) => {
  try {
    console.log('Fetching task list with params:', { taskName, pageNum })

    const params = {
      page_num: pageNum,
    }

    // Add task_name parameter only if provided (for fuzzy search)
    if (taskName && taskName.trim()) {
      params.task_name = taskName.trim()
    }

    const response = await axios.get('/api/v1/task_api/list', {
      params,
      headers: {
        token: localStorage.getItem('token'),
      },
    })

    console.log('Task list API response:', response.data)

    if (response.data.error_code === 0) {
      return {
        success: true,
        data: response.data.data || [],
        pagination: {
          page_num: response.data.page_num || 0,
          page_size: response.data.page_size || 10,
          total_page: response.data.total_page || 0,
        },
        message: response.data.message || 'success',
      }
    } else {
      console.error('Task list API error:', response.data.message)
      return {
        success: false,
        data: [],
        pagination: {
          page_num: 0,
          page_size: 10,
          total_page: 0,
        },
        message: response.data.message || '获取任务列表失败',
        error_code: response.data.error_code,
      }
    }
  } catch (error) {
    console.error('Failed to fetch task list:', error)

    // Use error handler for consistent error management
    const retryAction = () => fetchTaskList(taskName, pageNum)
    handleError(error, '获取任务列表失败', retryAction)

    return {
      success: false,
      data: [],
      pagination: {
        page_num: 0,
        page_size: 10,
        total_page: 0,
      },
      message: '网络错误，请稍后重试',
      error: error.message,
    }
  }
}

/**
 * Map API task data to component format
 * @param {Array} apiTasks - Task data from API
 * @returns {Array} Formatted task data for component use
 */
export const mapApiTasksToComponent = (apiTasks) => {
  if (!Array.isArray(apiTasks)) {
    return []
  }

  return apiTasks.map((task) => ({
    // Map API fields to component fields
    id: task.task_id,
    name: task.task_name || '',
    type: task.task_type || '',
    createdAt: task.crt_date || '',
    updatedAt: task.upd_time || '',
    status: mapTaskState(task.task_state),

    // Schedule configuration mapping
    config: {
      scheduleState: task.schedule_state || '',
      scheduleStartDt: task.schedule_start_dt || '',
      scheduleEndDt: task.schedule_end_dt || '',
      scheduleFrequency: task.schedule_frequency || '',
      scheduleHour: task.schedule_hour || '',
      scheduleMinute: task.schedule_minute || '',

      // Default configuration values (these might come from API in future)
      dataSource: '默认数据源',
      frequency: getFrequencyText(task.schedule_frequency),
      timeout: '30分钟',
      concurrency: '5',
      retryCount: '3',
      failureAction: '停止',
      logLevel: 'INFO',
    },

    // Initialize online status (not provided by API, default to false)
    online: false,
  }))
}

/**
 * Map API task state to component status
 * @param {string} taskState - Task state from API
 * @returns {string} Component status
 */
const mapTaskState = (taskState) => {
  const stateMapping = {
    completed: 'completed',
    running: 'running',
    failed: 'failed',
    pending: 'pending',
    paused: 'paused',
    stopped: 'failed',
    success: 'completed',
    error: 'failed',
  }

  return stateMapping[taskState?.toLowerCase()] || 'pending'
}

/**
 * Convert schedule frequency to readable text
 * @param {string} frequency - Schedule frequency from API
 * @returns {string} Readable frequency text
 */
const getFrequencyText = (frequency) => {
  const frequencyMapping = {
    daily: '每日',
    hourly: '每小时',
    weekly: '每周',
    monthly: '每月',
    once: '单次',
  }

  return frequencyMapping[frequency?.toLowerCase()] || '每日'
}

/**
 * Get mock task data for development/fallback
 * @returns {Array} Mock task data
 */
export const getMockTaskData = () => {
  return [
    {
      task_id: 1,
      task_name: '个贷催收场景分析',
      task_type: '引导式',
      crt_date: '2024-01-15T10:30:00',
      upd_time: '2024-01-16T14:20:00',
      task_state: 'completed',
      schedule_state: 'active',
      schedule_start_dt: '2024-01-15T10:00:00',
      schedule_end_dt: '2024-12-31T23:59:59',
      schedule_frequency: 'daily',
      schedule_hour: '10',
      schedule_minute: '00',
    },
    {
      task_id: 2,
      task_name: '信用卡逾期归因分析',
      task_type: '引导式',
      crt_date: '2024-01-15T14:20:00',
      upd_time: '2024-01-16T16:45:00',
      task_state: 'running',
      schedule_state: 'active',
      schedule_start_dt: '2024-01-15T14:00:00',
      schedule_end_dt: '2024-12-31T23:59:59',
      schedule_frequency: 'hourly',
      schedule_hour: '0',
      schedule_minute: '00',
    },
    {
      task_id: 3,
      task_name: '小微企业风险评估',
      task_type: '对话式',
      crt_date: '2024-01-14T16:45:00',
      upd_time: '2024-01-15T09:30:00',
      task_state: 'failed',
      schedule_state: 'inactive',
      schedule_start_dt: '2024-01-14T16:00:00',
      schedule_end_dt: '2024-12-31T23:59:59',
      schedule_frequency: 'weekly',
      schedule_hour: '16',
      schedule_minute: '00',
    },
    {
      task_id: 4,
      task_name: '消费金融场景分析',
      task_type: '引导式',
      crt_date: '2024-01-14T09:15:00',
      upd_time: '2024-01-15T11:20:00',
      task_state: 'completed',
      schedule_state: 'active',
      schedule_start_dt: '2024-01-14T09:00:00',
      schedule_end_dt: '2024-12-31T23:59:59',
      schedule_frequency: 'daily',
      schedule_hour: '11',
      schedule_minute: '00',
    },
  ]
}
